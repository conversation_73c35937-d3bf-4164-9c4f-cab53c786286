import { Vue, Component } from 'vue-property-decorator';
import DomDialog from 'components/dialog/DomDialog.vue';

@Component({
  name: 'HandleOperateDialog',
  components: { DomDialog },
})
export default class HandleOperateDialog extends Vue {
  $refs: any;
  confirmText = '确定';
  cancelText = '取消';
  form: any = {
    content: '',
  };
  title: string = '通过申请';
  contentText: string = '';
  isShowCancel: boolean = true;
  radio: string = '1'; // 是否有意见
  ProductLineList: any[] = [];
  onConfirm: (result: Nullable<string>) => void;
  onCancel: () => void;

  // 有意见
  get isHasSupport() {
    return this.radio === '1';
  }

  // 通过模式
  get isAgreeMode() {
    return this.title === '通过申请';
  }

  // 驳回模式
  get isRejectMode() {
    return this.title === '驳回申请';
  }

  // 补充模式
  get isAddMode() {
    return this.title === '添加意见';
  }

  // 转审模式
  get isPmcReassign() {
    return this.title === '转审';
  }

  // 校验规则
  get rules() {
    return {
      content: [
        {
          required: true,
          message: this.isPmcReassign ? '请输入转审原因' : '请输入驳回原因',
          trigger: 'blur',
        },
      ],
    };
  }

  doConfirm() {
    this.$refs.form.validate((valid) => {
      if (valid) {
        if (this.onConfirm) {
          let result: Nullable<string> = null;
          if (this.isAgreeMode && this.form.content.length > 0) {
            result = `通过，${this.form.content}`;
          } else if (this.isRejectMode) {
            result = `不通过，${this.form.content}`;
          } else if (this.isPmcReassign) {
            result = `转审，${this.form.content}`;
          } else if (this.isAddMode && this.isHasSupport) {
            // 至少填了一个意见
            const isHasOneComment = this.ProductLineList.some(
              (line) => this.form[line.id].length > 0
            );
            if (isHasOneComment) {
              result = this.ProductLineList.reduce((pre, cur) => {
                if (this.form[cur.id].length > 0) {
                  pre += `${cur.name}：${this.form[cur.id]}；`;
                }
                return pre;
              }, '');
              if (result) result = result.substring(0, result.length - 1) + `。`;
            } else {
              this.$message.error('审批意见不能为空');
              return;
            }
          }
          this.onConfirm(result);
        }
        this.$emit('hide');
      }
    });
  }
  doCancel() {
    if (this.onCancel) {
      this.onCancel();
    }
    this.$emit('hide');
  }
}
